# 路面生命周期碳排放计算管理平台 - 软件总体架构设计文档

## 1. 项目概述

路面生命周期碳排放计算管理平台是一个基于Web的碳排放计算和管理系统，主要用于道路铺面的碳排放评估、项目管理和数据分析。系统采用前后端分离的架构设计，前端使用Vue.js框架，后端基于Odoo框架构建。


## 3. 软件总体架构图

```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        A[Web浏览器]
        B[移动端浏览器]
    end
    
    subgraph "表现层 (Presentation Layer)"
        C[Vue.js前端应用]
        D[Vue Router路由管理]
        E[Vuex状态管理]
        F[Element UI组件]
        G[ECharts图表]
        H[Leaflet地图]
    end
    
    subgraph "接口层 (API Layer)"
        I[RESTful API接口]
        J[认证中间件]
        K[CORS处理]
        L[数据验证]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        M[碳排放计算服务]
        N[项目管理服务]
        O[清单管理服务]
        P[用户管理服务]
        Q[报表生成服务]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        R[Odoo ORM]
        S[模型定义]
        T[数据库操作]
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        U[(PostgreSQL数据库)]
        V[项目数据]
        W[清单数据]
        X[计算结果]
        Y[用户数据]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
    I --> J
    I --> K
    I --> L
    I --> M
    I --> N
    I --> O
    I --> P
    I --> Q
    M --> R
    N --> R
    O --> R
    P --> R
    Q --> R
    R --> S
    R --> T
    T --> U
    U --> V
    U --> W
    U --> X
    U --> Y
```

## 4. 层次架构详细说明

### 4.1 表现层 (Presentation Layer)
**职责**: 用户界面展示和交互处理
- **Vue.js应用**: 主应用框架，负责组件渲染和生命周期管理
- **路由管理**: 处理页面导航和权限控制
- **状态管理**: 管理应用全局状态和数据流
- **UI组件**: 提供统一的用户界面组件
- **数据可视化**: 图表和地图展示

### 4.2 接口层 (API Layer)
**职责**: 前后端数据交互和接口管理
- **RESTful API**: 提供标准化的HTTP接口
- **认证中间件**: 处理用户身份验证和会话管理
- **数据验证**: 验证请求参数和数据格式
- **跨域处理**: 处理CORS相关配置

### 4.3 业务逻辑层 (Business Logic Layer)
**职责**: 核心业务逻辑处理和计算
- **碳排放计算服务**: 实现粗算和精算两种计算模式
- **项目管理服务**: 处理项目创建、编辑、方案管理
- **清单管理服务**: 管理生命周期清单数据
- **用户管理服务**: 用户注册、登录、权限管理
- **报表生成服务**: 生成各类统计报表

### 4.4 数据访问层 (Data Access Layer)
**职责**: 数据库操作和对象关系映射
- **Odoo ORM**: 提供对象关系映射功能
- **模型定义**: 定义数据模型和关系
- **数据库操作**: 封装CRUD操作

### 4.5 数据存储层 (Data Storage Layer)
**职责**: 数据持久化存储
- **PostgreSQL数据库**: 主数据库
- **项目数据**: 存储碳排放项目信息
- **清单数据**: 存储生命周期清单
- **计算结果**: 存储碳排放计算结果
- **用户数据**: 存储用户信息和权限


## 6. 层次架构间关联计算方式

### 6.1 前端到后端数据流
```mermaid
sequenceDiagram
    participant F as 前端Vue应用
    participant A as Axios HTTP客户端
    participant P as 代理服务器
    participant R as RESTful API
    participant S as 业务服务层
    participant O as Odoo ORM
    participant D as PostgreSQL数据库

    F->>A: 发起API请求
    A->>P: HTTP请求(/bimclient/*)
    P->>R: 转发到后端API
    R->>S: 调用业务服务
    S->>O: ORM数据操作
    O->>D: SQL查询/更新
    D-->>O: 返回数据
    O-->>S: 返回模型对象
    S-->>R: 返回业务结果
    R-->>P: JSON响应
    P-->>A: 转发响应
    A-->>F: 返回数据
```

### 6.2 碳排放计算流程
```mermaid
flowchart TD
    A[项目数据输入] --> B{选择计算模式}
    B -->|粗算| C[方案比选计算]
    B -->|精算| D[碳排放核算]

    C --> E[获取结构层配置]
    D --> E

    E --> F[加载生命周期清单]
    F --> G[材料清单计算]
    F --> H[机械清单计算]
    F --> I[养护清单计算]
    F --> J[碳汇清单计算]

    G --> K[阶段性计算]
    H --> K
    I --> K
    J --> K

    K --> L[汇总计算结果]
    L --> M[生成多维度指标]
    M --> N[存储计算结果]
    N --> O[返回前端展示]
```

### 6.3 数据处理关联方式

#### 6.3.1 认证与会话管理
- 前端通过localStorage存储session信息
- 每个API请求携带X-Openerp-Session-Id头部
- 后端验证session有效性和用户权限

#### 6.3.2 数据验证与转换
- 前端使用Element UI进行基础数据验证
- 后端使用base_rest_datamodel进行数据模型验证
- 数据类型转换和格式化处理

#### 6.3.3 计算引擎关联
- 基于Odoo ORM的计算模型
- 支持多阶段计算（原料、运输、施工、养护等）
- 结果缓存和增量计算优化

## 7. 核心业务模块架构

### 7.1 项目管理模块
```mermaid
graph LR
    A[项目创建] --> B[基础信息录入]
    B --> C[选择生命周期清单]
    C --> D[配置计算参数]
    D --> E[创建计算方案]
    E --> F[执行计算]
    F --> G[结果分析]
    G --> H[报表生成]
```

### 7.2 清单管理模块
```mermaid
graph TB
    A[生命周期清单] --> B[材料清单]
    A --> C[机械清单]
    A --> D[养护清单]
    A --> E[碳汇清单]

    B --> F[结构层配置]
    C --> G[运输机械]
    C --> H[施工机械]
    C --> I[拆除机械]
    D --> J[病害处理]
    E --> K[碳汇因子]
```

### 7.3 计算引擎模块
```mermaid
graph TD
    A[计算请求] --> B{计算模式判断}
    B -->|rough| C[粗算引擎]
    B -->|fine| D[精算引擎]

    C --> E[基础参数计算]
    D --> F[详细参数计算]

    E --> G[阶段计算循环]
    F --> G

    G --> H[材料碳排放]
    G --> I[机械碳排放]
    G --> J[养护碳排放]
    G --> K[碳汇计算]

    H --> L[结果汇总]
    I --> L
    J --> L
    K --> L

    L --> M[多维度指标生成]
    M --> N[结果存储]
```

## 8. 系统特点与优势

### 8.1 架构特点
- **模块化设计**: 前后端分离，业务模块独立
- **可扩展性**: 基于Odoo框架，支持模块化扩展
- **标准化接口**: RESTful API设计，便于集成
- **数据驱动**: 基于生命周期清单的科学计算

### 8.2 技术优势
- **成熟框架**: Vue.js + Odoo双重保障
- **丰富组件**: Element UI + ECharts完整生态
- **灵活计算**: 支持多种计算模式和自定义配置
- **可视化强**: 地图展示 + 图表分析

### 8.3 业务优势
- **专业性强**: 专注于路面碳排放计算领域
- **数据完整**: 覆盖全生命周期各个阶段
- **结果可靠**: 基于科学的计算模型和标准
- **易于使用**: 直观的用户界面和操作流程

## 9. 部署架构

### 9.1 开发环境
- 前端: Vue CLI开发服务器 (端口8080)
- 后端: Odoo开发服务器 (端口8069)
- 数据库: PostgreSQL

### 9.2 生产环境
- 前端: Nginx静态文件服务
- 后端: Odoo应用服务器
- 数据库: PostgreSQL集群
- 负载均衡: Nginx反向代理

---

*本文档版本: v1.0*
*最后更新: 2025-07-24*
```
